com.technews.tech_news_app-core-runtime-2.2.0-0 /Users/<USER>/.gradle/caches/transforms-3/0073f776ec986342f7d8a627afdc659a/transformed/core-runtime-2.2.0/res
com.technews.tech_news_app-jetified-window-java-1.2.0-1 /Users/<USER>/.gradle/caches/transforms-3/0157f8871f2286b81f0b60455cb44671/transformed/jetified-window-java-1.2.0/res
com.technews.tech_news_app-jetified-appcompat-resources-1.1.0-2 /Users/<USER>/.gradle/caches/transforms-3/179d597149d8b44255724080d49dad56/transformed/jetified-appcompat-resources-1.1.0/res
com.technews.tech_news_app-jetified-savedstate-ktx-1.2.1-3 /Users/<USER>/.gradle/caches/transforms-3/1b2f8e1ba94273c251ea978a05cc081c/transformed/jetified-savedstate-ktx-1.2.1/res
com.technews.tech_news_app-preference-1.2.1-4 /Users/<USER>/.gradle/caches/transforms-3/1c848f8f37c5e484fe559c7671e5ae91/transformed/preference-1.2.1/res
com.technews.tech_news_app-jetified-datastore-release-5 /Users/<USER>/.gradle/caches/transforms-3/2176a0aa5f6bbb15843e9d6617ca94fc/transformed/jetified-datastore-release/res
com.technews.tech_news_app-core-1.13.1-6 /Users/<USER>/.gradle/caches/transforms-3/28ee8a5ebc96d117de5598ee6fce01ba/transformed/core-1.13.1/res
com.technews.tech_news_app-jetified-tracing-1.2.0-7 /Users/<USER>/.gradle/caches/transforms-3/344804b99488c687b9494581b018a8c9/transformed/jetified-tracing-1.2.0/res
com.technews.tech_news_app-slidingpanelayout-1.2.0-8 /Users/<USER>/.gradle/caches/transforms-3/3acec481d9fcce33190fa922d3460712/transformed/slidingpanelayout-1.2.0/res
com.technews.tech_news_app-jetified-savedstate-1.2.1-9 /Users/<USER>/.gradle/caches/transforms-3/42e115ca81393979cdff36cce874f0ab/transformed/jetified-savedstate-1.2.1/res
com.technews.tech_news_app-recyclerview-1.0.0-10 /Users/<USER>/.gradle/caches/transforms-3/4346eeaa665b3533cb1f34b1539bb5b7/transformed/recyclerview-1.0.0/res
com.technews.tech_news_app-jetified-datastore-preferences-release-11 /Users/<USER>/.gradle/caches/transforms-3/43b95863739acfc52281eeb9260d3a6b/transformed/jetified-datastore-preferences-release/res
com.technews.tech_news_app-jetified-profileinstaller-1.3.1-12 /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/res
com.technews.tech_news_app-jetified-core-1.0.0-13 /Users/<USER>/.gradle/caches/transforms-3/4ff43cf3d056aae38d02ffded0cc6e24/transformed/jetified-core-1.0.0/res
com.technews.tech_news_app-jetified-lifecycle-livedata-core-ktx-2.7.0-14 /Users/<USER>/.gradle/caches/transforms-3/53851e427f91564da59a935205158479/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/res
com.technews.tech_news_app-jetified-core-ktx-1.13.1-15 /Users/<USER>/.gradle/caches/transforms-3/5847454f47d031053937666e3a5e82d8/transformed/jetified-core-ktx-1.13.1/res
com.technews.tech_news_app-jetified-lifecycle-viewmodel-savedstate-2.7.0-16 /Users/<USER>/.gradle/caches/transforms-3/5faa35d8b556276be34f51d63001a29f/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/res
com.technews.tech_news_app-lifecycle-viewmodel-2.7.0-17 /Users/<USER>/.gradle/caches/transforms-3/6066a53547e8fbcf4f45946ad85f0e5d/transformed/lifecycle-viewmodel-2.7.0/res
com.technews.tech_news_app-transition-1.4.1-18 /Users/<USER>/.gradle/caches/transforms-3/607e5dd937f355d4223d454e45c3189b/transformed/transition-1.4.1/res
com.technews.tech_news_app-fragment-1.7.1-19 /Users/<USER>/.gradle/caches/transforms-3/63d3b185799b2c098a0f6ed885ea8db1/transformed/fragment-1.7.1/res
com.technews.tech_news_app-browser-1.8.0-20 /Users/<USER>/.gradle/caches/transforms-3/6bf7b9bec3a755c3e68569d2ddbec360/transformed/browser-1.8.0/res
com.technews.tech_news_app-jetified-annotation-experimental-1.4.1-21 /Users/<USER>/.gradle/caches/transforms-3/6cbd5b3909adf114142d7dff065bfeaa/transformed/jetified-annotation-experimental-1.4.1/res
com.technews.tech_news_app-jetified-lifecycle-process-2.7.0-22 /Users/<USER>/.gradle/caches/transforms-3/6f1de229824e3b626519c3b4ed1eee9b/transformed/jetified-lifecycle-process-2.7.0/res
com.technews.tech_news_app-jetified-startup-runtime-1.1.1-23 /Users/<USER>/.gradle/caches/transforms-3/7794f10e2cc1161d7666b6dd70a8eed0/transformed/jetified-startup-runtime-1.1.1/res
com.technews.tech_news_app-appcompat-1.1.0-24 /Users/<USER>/.gradle/caches/transforms-3/799d1c16666fc18cb929ebf700d90383/transformed/appcompat-1.1.0/res
com.technews.tech_news_app-jetified-activity-ktx-1.8.1-25 /Users/<USER>/.gradle/caches/transforms-3/9b4afd898c368fc70f62240b6a671ca9/transformed/jetified-activity-ktx-1.8.1/res
com.technews.tech_news_app-jetified-window-1.2.0-26 /Users/<USER>/.gradle/caches/transforms-3/a017c0c1c150e766625df730ca1d1522/transformed/jetified-window-1.2.0/res
com.technews.tech_news_app-jetified-lifecycle-viewmodel-ktx-2.7.0-27 /Users/<USER>/.gradle/caches/transforms-3/a3748a8ad00736d44691d03a3af6bdd6/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/res
com.technews.tech_news_app-coordinatorlayout-1.0.0-28 /Users/<USER>/.gradle/caches/transforms-3/a6152668d1a27e461f944bc1fd9e12ca/transformed/coordinatorlayout-1.0.0/res
com.technews.tech_news_app-lifecycle-livedata-core-2.7.0-29 /Users/<USER>/.gradle/caches/transforms-3/a66a87692f835751c9cebc5e6aca319b/transformed/lifecycle-livedata-core-2.7.0/res
com.technews.tech_news_app-jetified-activity-1.8.1-30 /Users/<USER>/.gradle/caches/transforms-3/aecaa94fd3010ebbc81c2fe40f782abe/transformed/jetified-activity-1.8.1/res
com.technews.tech_news_app-jetified-fragment-ktx-1.7.1-31 /Users/<USER>/.gradle/caches/transforms-3/b8c188175ec7b416f0fdcca7a679e9cb/transformed/jetified-fragment-ktx-1.7.1/res
com.technews.tech_news_app-webkit-1.12.1-32 /Users/<USER>/.gradle/caches/transforms-3/be292e28d2ab560e24fce328aa4e3c48/transformed/webkit-1.12.1/res
com.technews.tech_news_app-lifecycle-livedata-2.7.0-33 /Users/<USER>/.gradle/caches/transforms-3/c11b0fd8d42c1350d7f4e72974d231fa/transformed/lifecycle-livedata-2.7.0/res
com.technews.tech_news_app-jetified-lifecycle-runtime-ktx-2.7.0-34 /Users/<USER>/.gradle/caches/transforms-3/c215d1ae83aa15da64dde0f87332ab32/transformed/jetified-lifecycle-runtime-ktx-2.7.0/res
com.technews.tech_news_app-lifecycle-runtime-2.7.0-35 /Users/<USER>/.gradle/caches/transforms-3/c3e60d8edb6a86f432de0791fcaa87bd/transformed/lifecycle-runtime-2.7.0/res
com.technews.tech_news_app-jetified-datastore-core-release-36 /Users/<USER>/.gradle/caches/transforms-3/da3486b01469509e0b30ca8fa5e1a3ee/transformed/jetified-datastore-core-release/res
com.technews.tech_news_app-debug-37 /Users/<USER>/Documents/augment-projects/tech-news-flutter/android/app/src/debug/res
com.technews.tech_news_app-main-38 /Users/<USER>/Documents/augment-projects/tech-news-flutter/android/app/src/main/res
com.technews.tech_news_app-pngs-39 /Users/<USER>/Documents/augment-projects/tech-news-flutter/build/app/generated/res/pngs/debug
com.technews.tech_news_app-resValues-40 /Users/<USER>/Documents/augment-projects/tech-news-flutter/build/app/generated/res/resValues/debug
com.technews.tech_news_app-packageDebugResources-41 /Users/<USER>/Documents/augment-projects/tech-news-flutter/build/app/intermediates/incremental/debug/packageDebugResources/merged.dir
com.technews.tech_news_app-packageDebugResources-42 /Users/<USER>/Documents/augment-projects/tech-news-flutter/build/app/intermediates/incremental/debug/packageDebugResources/stripped.dir
com.technews.tech_news_app-merged_res-43 /Users/<USER>/Documents/augment-projects/tech-news-flutter/build/app/intermediates/merged_res/debug
com.technews.tech_news_app-packaged_res-44 /Users/<USER>/Documents/augment-projects/tech-news-flutter/build/connectivity_plus/intermediates/packaged_res/debug
com.technews.tech_news_app-packaged_res-45 /Users/<USER>/Documents/augment-projects/tech-news-flutter/build/flutter_native_splash/intermediates/packaged_res/debug
com.technews.tech_news_app-packaged_res-46 /Users/<USER>/Documents/augment-projects/tech-news-flutter/build/shared_preferences_android/intermediates/packaged_res/debug
com.technews.tech_news_app-packaged_res-47 /Users/<USER>/Documents/augment-projects/tech-news-flutter/build/url_launcher_android/intermediates/packaged_res/debug
com.technews.tech_news_app-packaged_res-48 /Users/<USER>/Documents/augment-projects/tech-news-flutter/build/webview_flutter_android/intermediates/packaged_res/debug
