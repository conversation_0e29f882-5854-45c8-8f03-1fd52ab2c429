{"logs": [{"outputFile": "com.technews.tech_news_app-mergeDebugResources-40:/values-bs/values-bs.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/1c848f8f37c5e484fe559c7671e5ae91/transformed/preference-1.2.1/res/values-bs/values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,346,484,653,740", "endColumns": "70,86,82,137,168,86,82", "endOffsets": "171,258,341,479,648,735,818"}, "to": {"startLines": "36,37,38,39,42,43,44", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3555,3626,3713,3796,4121,4290,4377", "endColumns": "70,86,82,137,168,86,82", "endOffsets": "3621,3708,3791,3929,4285,4372,4455"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/799d1c16666fc18cb929ebf700d90383/transformed/appcompat-1.1.0/res/values-bs/values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,910,1001,1093,1188,1282,1383,1476,1571,1666,1757,1848,1935,2038,2142,2243,2348,2462,2565,2734,2830", "endColumns": "120,96,106,85,103,121,84,82,90,91,94,93,100,92,94,94,90,90,86,102,103,100,104,113,102,168,95,85", "endOffsets": "221,318,425,511,615,737,822,905,996,1088,1183,1277,1378,1471,1566,1661,1752,1843,1930,2033,2137,2238,2343,2457,2560,2729,2825,2911"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,910,1001,1093,1188,1282,1383,1476,1571,1666,1757,1848,1935,2038,2142,2243,2348,2462,2565,2734,3934", "endColumns": "120,96,106,85,103,121,84,82,90,91,94,93,100,92,94,94,90,90,86,102,103,100,104,113,102,168,95,85", "endOffsets": "221,318,425,511,615,737,822,905,996,1088,1183,1277,1378,1471,1566,1661,1752,1843,1930,2033,2137,2238,2343,2457,2560,2729,2825,4015"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/28ee8a5ebc96d117de5598ee6fce01ba/transformed/core-1.13.1/res/values-bs/values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "29,30,31,32,33,34,35,41", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2830,2928,3030,3128,3232,3336,3438,4020", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "2923,3025,3123,3227,3331,3433,3550,4116"}}]}]}