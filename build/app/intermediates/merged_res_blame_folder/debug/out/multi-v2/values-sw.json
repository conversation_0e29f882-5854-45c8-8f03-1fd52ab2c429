{"logs": [{"outputFile": "com.technews.tech_news_app-mergeDebugResources-41:/values-sw/values-sw.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/6bf7b9bec3a755c3e68569d2ddbec360/transformed/browser-1.8.0/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,270,387", "endColumns": "113,100,116,102", "endOffsets": "164,265,382,485"}, "to": {"startLines": "37,39,40,41", "startColumns": "4,4,4,4", "startOffsets": "3594,3805,3906,4023", "endColumns": "113,100,116,102", "endOffsets": "3703,3901,4018,4121"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/28ee8a5ebc96d117de5598ee6fce01ba/transformed/core-1.13.1/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "29,30,31,32,33,34,35,45", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2801,2895,2997,3094,3195,3302,3409,4435", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "2890,2992,3089,3190,3297,3404,3519,4531"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/1c848f8f37c5e484fe559c7671e5ae91/transformed/preference-1.2.1/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,349,499,668,753", "endColumns": "69,96,76,149,168,84,82", "endOffsets": "170,267,344,494,663,748,831"}, "to": {"startLines": "36,38,42,43,46,47,48", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3524,3708,4126,4203,4536,4705,4790", "endColumns": "69,96,76,149,168,84,82", "endOffsets": "3589,3800,4198,4348,4700,4785,4868"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/799d1c16666fc18cb929ebf700d90383/transformed/appcompat-1.1.0/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,893,984,1076,1171,1265,1366,1459,1554,1648,1739,1830,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,82,90,91,94,93,100,92,94,93,90,90,80,100,107,98,106,111,103,161,96,81", "endOffsets": "203,302,410,500,605,722,805,888,979,1071,1166,1260,1361,1454,1549,1643,1734,1825,1906,2007,2115,2214,2321,2433,2537,2699,2796,2878"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,893,984,1076,1171,1265,1366,1459,1554,1648,1739,1830,1911,2012,2120,2219,2326,2438,2542,2704,4353", "endColumns": "102,98,107,89,104,116,82,82,90,91,94,93,100,92,94,93,90,90,80,100,107,98,106,111,103,161,96,81", "endOffsets": "203,302,410,500,605,722,805,888,979,1071,1166,1260,1361,1454,1549,1643,1734,1825,1906,2007,2115,2214,2321,2433,2537,2699,2796,4430"}}]}]}