{"logs": [{"outputFile": "com.technews.tech_news_app-mergeDebugResources-41:/values-uz/values-uz.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/799d1c16666fc18cb929ebf700d90383/transformed/appcompat-1.1.0/res/values-uz/values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,868,959,1051,1146,1240,1335,1428,1523,1618,1709,1801,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,78,90,91,94,93,94,92,94,94,90,91,82,109,105,99,107,105,101,160,98,82", "endOffsets": "205,300,400,482,582,699,784,863,954,1046,1141,1235,1330,1423,1518,1613,1704,1796,1879,1989,2095,2195,2303,2409,2511,2672,2771,2854"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,868,959,1051,1146,1240,1335,1428,1523,1618,1709,1801,1884,1994,2100,2200,2308,2414,2516,2677,4348", "endColumns": "104,94,99,81,99,116,84,78,90,91,94,93,94,92,94,94,90,91,82,109,105,99,107,105,101,160,98,82", "endOffsets": "205,300,400,482,582,699,784,863,954,1046,1141,1235,1330,1423,1518,1613,1704,1796,1879,1989,2095,2195,2303,2409,2511,2672,2771,4426"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/28ee8a5ebc96d117de5598ee6fce01ba/transformed/core-1.13.1/res/values-uz/values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "29,30,31,32,33,34,35,45", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2776,2878,2980,3081,3181,3289,3393,4431", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "2873,2975,3076,3176,3284,3388,3507,4527"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/1c848f8f37c5e484fe559c7671e5ae91/transformed/preference-1.2.1/res/values-uz/values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,180,273,353,498,667,754", "endColumns": "74,92,79,144,168,86,78", "endOffsets": "175,268,348,493,662,749,828"}, "to": {"startLines": "36,38,42,43,46,47,48", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3512,3705,4123,4203,4532,4701,4788", "endColumns": "74,92,79,144,168,86,78", "endOffsets": "3582,3793,4198,4343,4696,4783,4862"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/6bf7b9bec3a755c3e68569d2ddbec360/transformed/browser-1.8.0/res/values-uz/values-uz.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,173,282,392", "endColumns": "117,108,109,105", "endOffsets": "168,277,387,493"}, "to": {"startLines": "37,39,40,41", "startColumns": "4,4,4,4", "startOffsets": "3587,3798,3907,4017", "endColumns": "117,108,109,105", "endOffsets": "3700,3902,4012,4118"}}]}]}