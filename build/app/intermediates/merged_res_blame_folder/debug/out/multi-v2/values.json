{"logs": [{"outputFile": "com.technews.tech_news_app-mergeDebugResources-41:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/42e115ca81393979cdff36cce874f0ab/transformed/jetified-savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "349", "startColumns": "4", "startOffsets": "20671", "endColumns": "53", "endOffsets": "20720"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/607e5dd937f355d4223d454e45c3189b/transformed/transition-1.4.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "308,309,314,321,322,341,342,343,344,345", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "18603,18643,18860,19198,19253,20270,20324,20376,20425,20486", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "18638,18685,18898,19248,19295,20319,20371,20420,20481,20531"}}, {"source": "/Users/<USER>/Documents/augment-projects/tech-news-flutter/android/app/src/main/res/values/styles.xml", "from": {"startLines": "3,18", "startColumns": "4,4", "startOffsets": "173,1089", "endLines": "11,20", "endColumns": "12,12", "endOffsets": "747,1253"}, "to": {"startLines": "1498,1506", "startColumns": "4,4", "startOffsets": "94257,94709", "endLines": "1505,1508", "endColumns": "12,12", "endOffsets": "94704,94873"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/799d1c16666fc18cb929ebf700d90383/transformed/appcompat-1.1.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11726,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11781,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,222,223,227,228,229,230,231,232,233,259,260,261,262,263,264,265,266,302,303,304,305,310,318,319,324,346,352,353,354,355,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,404,409,410,411,412,413,414,422,423,427,431,435,440,446,453,457,461,466,470,474,478,482,486,490,496,500,506,510,516,520,525,529,532,536,542,546,552,556,562,565,569,573,577,581,585,586,587,588,591,594,597,600,604,605,606,607,608,611,613,615,617,622,623,627,633,637,638,640,651,652,656,662,666,667,668,672,699,703,704,708,736,906,932,1103,1129,1160,1168,1174,1188,1210,1215,1220,1230,1239,1248,1252,1259,1267,1274,1275,1284,1287,1290,1294,1298,1302,1305,1306,1311,1316,1326,1331,1338,1344,1345,1348,1352,1357,1359,1361,1364,1367,1369,1373,1376,1383,1386,1389,1393,1395,1399,1401,1403,1405,1409,1417,1425,1437,1443,1452,1455,1466,1469,1470,1475,1476,1509,1578,1648,1649,1659,1668,1820,1822,1826,1829,1832,1835,1838,1841,1844,1847,1851,1854,1857,1860,1864,1867,1871,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1897,1899,1900,1901,1902,1903,1904,1905,1906,1908,1909,1911,1912,1914,1916,1917,1919,1920,1921,1922,1923,1924,1926,1927,1928,1929,1930,1942,1944,1946,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1962,1963,1964,1965,1966,1967,1969,1973,1977,1978,1979,1980,1981,1982,1986,1987,1988,1989,1991,1993,1995,1997,1999,2000,2001,2002,2004,2006,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2022,2023,2024,2025,2027,2029,2030,2032,2033,2035,2037,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2052,2053,2054,2055,2057,2058,2059,2060,2061,2063,2065,2067,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2089,2164,2167,2170,2173,2187,2204,2246,2275,2302,2311,2373,2737,2768,2906,3030,3054,3060,3076,3097,3221,3249,3255,3399,3425,3473,3544,3644,3664,3719,3731,3757", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2138,2273,2354,2415,2490,2566,2643,2881,2966,3048,3124,3200,3277,3355,3461,3567,3646,3975,4032,4221,4295,4370,4435,4501,4561,4622,4694,4767,4834,4902,4961,5020,5079,5138,5197,5251,5305,5358,5412,5466,5520,5775,5849,5928,6001,6075,6146,6218,6290,6363,6420,6478,6551,6625,6699,6774,6846,6919,6989,7060,7120,7181,7250,7319,7389,7463,7539,7603,7680,7756,7833,7898,7967,8044,8119,8188,8256,8333,8399,8460,8557,8622,8691,8790,8861,8920,8978,9035,9094,9158,9229,9301,9373,9445,9517,9584,9652,9720,9779,9842,9906,9996,10087,10147,10213,10280,10346,10416,10480,10533,10600,10661,10728,10841,10899,10962,11027,11092,11167,11240,11312,11361,11422,11483,11544,11606,11670,11734,11798,11863,11926,11986,12047,12113,12172,12232,12294,12365,12425,13124,13210,13460,13550,13637,13725,13807,13890,13980,15705,15757,15815,15860,15926,15990,16047,16104,18281,18338,18386,18435,18690,19060,19107,19365,20536,20839,20903,20965,21025,21278,21352,21422,21500,21554,21624,21709,21757,21803,21864,21927,21993,22057,22128,22191,22256,22320,22381,22442,22494,22567,22641,22710,22785,22859,22933,23074,24300,24661,24739,24829,24917,25013,25103,25685,25774,26021,26302,26554,26839,27232,27709,27931,28153,28429,28656,28886,29116,29346,29576,29803,30222,30448,30873,31103,31531,31750,32033,32241,32372,32599,33025,33250,33677,33898,34323,34443,34719,35020,35344,35635,35949,36086,36217,36322,36564,36731,36935,37143,37414,37526,37638,37743,37860,38074,38220,38360,38446,38794,38882,39128,39546,39795,39877,39975,40567,40667,40919,41343,41598,41692,41781,42018,44042,44284,44386,44639,46795,57327,58843,69474,71002,72759,73385,73805,74866,76131,76387,76623,77170,77664,78269,78467,79047,79611,79986,80104,80642,80799,80995,81268,81524,81694,81835,81899,82264,82631,83307,83571,83909,84262,84356,84542,84848,85110,85235,85362,85601,85812,85931,86124,86301,86756,86937,87059,87318,87431,87618,87720,87827,87956,88231,88739,89235,90112,90406,90976,91125,91857,92029,92113,92449,92541,94878,100124,105513,105575,106153,106737,114684,114797,115026,115186,115338,115509,115675,115844,116011,116174,116417,116587,116760,116931,117205,117404,117609,117939,118023,118119,118215,118313,118413,118515,118617,118719,118821,118923,119023,119119,119231,119360,119483,119614,119745,119843,119957,120051,120191,120325,120421,120533,120633,120749,120845,120957,121057,121197,121333,121497,121627,121785,121935,122076,122220,122355,122467,122617,122745,122873,123009,123141,123271,123401,123513,124411,124557,124701,124839,124905,124995,125071,125175,125265,125367,125475,125583,125683,125763,125855,125953,126063,126141,126247,126339,126443,126553,126675,126838,126995,127075,127175,127265,127375,127465,127706,127800,127906,127998,128098,128210,128324,128440,128556,128650,128764,128876,128978,129098,129220,129302,129406,129526,129652,129750,129844,129932,130044,130160,130282,130394,130569,130685,130771,130863,130975,131099,131166,131292,131360,131488,131632,131760,131829,131924,132039,132152,132251,132360,132471,132582,132683,132788,132888,133018,133109,133232,133326,133438,133524,133628,133724,133812,133930,134034,134138,134264,134352,134460,134560,134650,134760,134844,134946,135030,135084,135148,135254,135340,135450,135534,135938,138554,138672,138787,138867,139228,139814,141218,142562,143923,144311,147086,157175,158215,165028,169329,170080,170342,170874,171253,175531,176385,176614,181222,182232,183767,186167,190291,191035,193166,193506,194817", "endLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,222,223,227,228,229,230,231,232,233,259,260,261,262,263,264,265,266,302,303,304,305,310,318,319,324,346,352,353,354,355,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,404,409,410,411,412,413,421,422,426,430,434,439,445,452,456,460,465,469,473,477,481,485,489,495,499,505,509,515,519,524,528,531,535,541,545,551,555,561,564,568,572,576,580,584,585,586,587,590,593,596,599,603,604,605,606,607,610,612,614,616,621,622,626,632,636,637,639,650,651,655,661,665,666,667,671,698,702,703,707,735,905,931,1102,1128,1159,1167,1173,1187,1209,1214,1219,1229,1238,1247,1251,1258,1266,1273,1274,1283,1286,1289,1293,1297,1301,1304,1305,1310,1315,1325,1330,1337,1343,1344,1347,1351,1356,1358,1360,1363,1366,1368,1372,1375,1382,1385,1388,1392,1394,1398,1400,1402,1404,1408,1416,1424,1436,1442,1451,1454,1465,1468,1469,1474,1475,1480,1577,1647,1648,1658,1667,1668,1821,1825,1828,1831,1834,1837,1840,1843,1846,1850,1853,1856,1859,1863,1866,1870,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1896,1898,1899,1900,1901,1902,1903,1904,1905,1907,1908,1910,1911,1913,1915,1916,1918,1919,1920,1921,1922,1923,1925,1926,1927,1928,1929,1930,1943,1945,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1961,1962,1963,1964,1965,1966,1968,1972,1976,1977,1978,1979,1980,1981,1985,1986,1987,1988,1990,1992,1994,1996,1998,1999,2000,2001,2003,2005,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2021,2022,2023,2024,2026,2028,2029,2031,2032,2034,2036,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2051,2052,2053,2054,2056,2057,2058,2059,2060,2062,2064,2066,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2163,2166,2169,2172,2186,2192,2213,2274,2301,2310,2372,2731,2740,2795,2923,3053,3059,3065,3096,3220,3240,3254,3258,3404,3459,3484,3609,3663,3718,3730,3756,3763", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2133,2195,2349,2410,2485,2561,2638,2716,2961,3043,3119,3195,3272,3350,3456,3562,3641,3721,4027,4085,4290,4365,4430,4496,4556,4617,4689,4762,4829,4897,4956,5015,5074,5133,5192,5246,5300,5353,5407,5461,5515,5569,5844,5923,5996,6070,6141,6213,6285,6358,6415,6473,6546,6620,6694,6769,6841,6914,6984,7055,7115,7176,7245,7314,7384,7458,7534,7598,7675,7751,7828,7893,7962,8039,8114,8183,8251,8328,8394,8455,8552,8617,8686,8785,8856,8915,8973,9030,9089,9153,9224,9296,9368,9440,9512,9579,9647,9715,9774,9837,9901,9991,10082,10142,10208,10275,10341,10411,10475,10528,10595,10656,10723,10836,10894,10957,11022,11087,11162,11235,11307,11356,11417,11478,11539,11601,11665,11729,11793,11858,11921,11981,12042,12108,12167,12227,12289,12360,12420,12488,13205,13292,13545,13632,13720,13802,13885,13975,14066,15752,15810,15855,15921,15985,16042,16099,16153,18333,18381,18430,18481,18719,19102,19151,19406,20563,20898,20960,21020,21077,21347,21417,21495,21549,21619,21704,21752,21798,21859,21922,21988,22052,22123,22186,22251,22315,22376,22437,22489,22562,22636,22705,22780,22854,22928,23069,23139,24348,24734,24824,24912,25008,25098,25680,25769,26016,26297,26549,26834,27227,27704,27926,28148,28424,28651,28881,29111,29341,29571,29798,30217,30443,30868,31098,31526,31745,32028,32236,32367,32594,33020,33245,33672,33893,34318,34438,34714,35015,35339,35630,35944,36081,36212,36317,36559,36726,36930,37138,37409,37521,37633,37738,37855,38069,38215,38355,38441,38789,38877,39123,39541,39790,39872,39970,40562,40662,40914,41338,41593,41687,41776,42013,44037,44279,44381,44634,46790,57322,58838,69469,70997,72754,73380,73800,74861,76126,76382,76618,77165,77659,78264,78462,79042,79606,79981,80099,80637,80794,80990,81263,81519,81689,81830,81894,82259,82626,83302,83566,83904,84257,84351,84537,84843,85105,85230,85357,85596,85807,85926,86119,86296,86751,86932,87054,87313,87426,87613,87715,87822,87951,88226,88734,89230,90107,90401,90971,91120,91852,92024,92108,92444,92536,92814,100119,105508,105570,106148,106732,106823,114792,115021,115181,115333,115504,115670,115839,116006,116169,116412,116582,116755,116926,117200,117399,117604,117934,118018,118114,118210,118308,118408,118510,118612,118714,118816,118918,119018,119114,119226,119355,119478,119609,119740,119838,119952,120046,120186,120320,120416,120528,120628,120744,120840,120952,121052,121192,121328,121492,121622,121780,121930,122071,122215,122350,122462,122612,122740,122868,123004,123136,123266,123396,123508,123648,124552,124696,124834,124900,124990,125066,125170,125260,125362,125470,125578,125678,125758,125850,125948,126058,126136,126242,126334,126438,126548,126670,126833,126990,127070,127170,127260,127370,127460,127701,127795,127901,127993,128093,128205,128319,128435,128551,128645,128759,128871,128973,129093,129215,129297,129401,129521,129647,129745,129839,129927,130039,130155,130277,130389,130564,130680,130766,130858,130970,131094,131161,131287,131355,131483,131627,131755,131824,131919,132034,132147,132246,132355,132466,132577,132678,132783,132883,133013,133104,133227,133321,133433,133519,133623,133719,133807,133925,134029,134133,134259,134347,134455,134555,134645,134755,134839,134941,135025,135079,135143,135249,135335,135445,135529,135649,138549,138667,138782,138862,139223,139456,140326,142557,143918,144306,147081,156985,157305,159567,165595,170075,170337,170537,171248,175526,176132,176609,176760,181432,183310,184074,189188,191030,193161,193501,194812,195015"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/28ee8a5ebc96d117de5598ee6fce01ba/transformed/core-1.13.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "29,70,71,88,89,112,113,215,216,217,218,219,220,221,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,312,313,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,358,387,388,389,390,391,392,393,405,1931,1932,1936,1937,1941,2084,2085,2741,2758,2928,2961,2991,3024", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2721,2793,4090,4155,5574,5643,12636,12706,12774,12846,12916,12977,13051,14294,14355,14416,14478,14542,14604,14665,14733,14833,14893,14959,15032,15101,15158,15210,16158,16230,16306,16371,16430,16489,16549,16609,16669,16729,16789,16849,16909,16969,17029,17089,17148,17208,17268,17328,17388,17448,17508,17568,17628,17688,17748,17807,17867,17927,17986,18045,18104,18163,18222,18790,18825,19411,19466,19529,19584,19642,19700,19761,19824,19881,19932,19982,20043,20100,20166,20200,20235,21208,23227,23294,23366,23435,23504,23578,23650,24353,123653,123770,123971,124081,124282,135654,135726,157310,157914,165749,167480,168480,169162", "endLines": "29,70,71,88,89,112,113,215,216,217,218,219,220,221,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,312,313,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,358,387,388,389,390,391,392,393,405,1931,1935,1936,1940,1941,2084,2085,2746,2767,2960,2981,3023,3029", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,2788,2876,4150,4216,5638,5701,12701,12769,12841,12911,12972,13046,13119,14350,14411,14473,14537,14599,14660,14728,14828,14888,14954,15027,15096,15153,15205,15267,16225,16301,16366,16425,16484,16544,16604,16664,16724,16784,16844,16904,16964,17024,17084,17143,17203,17263,17323,17383,17443,17503,17563,17623,17683,17743,17802,17862,17922,17981,18040,18099,18158,18217,18276,18820,18855,19461,19524,19579,19637,19695,19756,19819,19876,19927,19977,20038,20095,20161,20195,20230,20265,21273,23289,23361,23430,23499,23573,23645,23733,24419,123765,123966,124076,124277,124406,135721,135788,157508,158210,167475,168156,169157,169324"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/63d3b185799b2c098a0f6ed885ea8db1/transformed/fragment-1.7.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "307,323,351,2982,2987", "startColumns": "4,4,4,4,4", "startOffsets": "18546,19300,20775,168161,168331", "endLines": "307,323,351,2986,2990", "endColumns": "56,64,63,24,24", "endOffsets": "18598,19360,20834,168326,168475"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/6066a53547e8fbcf4f45946ad85f0e5d/transformed/lifecycle-viewmodel-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "350", "startColumns": "4", "startOffsets": "20725", "endColumns": "49", "endOffsets": "20770"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/7794f10e2cc1161d7666b6dd70a8eed0/transformed/jetified-startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "386", "startColumns": "4", "startOffsets": "23144", "endColumns": "82", "endOffsets": "23222"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/aecaa94fd3010ebbc81c2fe40f782abe/transformed/jetified-activity-1.8.1/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "320,348", "startColumns": "4,4", "startOffsets": "19156,20611", "endColumns": "41,59", "endOffsets": "19193,20666"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/1c848f8f37c5e484fe559c7671e5ae91/transformed/preference-1.2.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "63,114,252,253,254,255,256,257,258,315,316,317,356,357,394,396,400,401,406,407,408,1481,1669,1672,1678,1684,1687,1693,1697,1700,1707,1713,1716,1722,1727,1732,1739,1741,1747,1753,1761,1766,1773,1778,1784,1788,1795,1799,1805,1811,1814,1818,1819,2732,2747,2886,2924,3066,3241,3259,3323,3333,3343,3350,3356,3460,3610,3627", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2200,5706,15272,15336,15391,15459,15526,15591,15648,18903,18951,18999,21082,21145,23738,23844,24116,24160,24424,24563,24613,92819,106828,106933,107178,107516,107662,108002,108214,108377,108784,109122,109245,109584,109823,110080,110451,110511,110849,111135,111584,111876,112264,112569,112913,113158,113488,113695,113963,114236,114380,114581,114628,156990,157513,164299,165600,170542,176137,176765,178690,178972,179277,179539,179799,183315,189193,189723", "endLines": "63,114,252,253,254,255,256,257,258,315,316,317,356,357,394,396,400,403,406,407,408,1497,1671,1677,1683,1686,1692,1696,1699,1706,1712,1715,1721,1726,1731,1738,1740,1746,1752,1760,1765,1772,1777,1783,1787,1794,1798,1804,1810,1813,1817,1818,1819,2736,2757,2905,2927,3075,3248,3322,3332,3342,3349,3355,3398,3472,3626,3643", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2268,5770,15331,15386,15454,15521,15586,15643,15700,18946,18994,19055,21140,21203,23771,23896,24155,24295,24558,24608,24656,94252,106928,107173,107511,107657,107997,108209,108372,108779,109117,109240,109579,109818,110075,110446,110506,110844,111130,111579,111871,112259,112564,112908,113153,113483,113690,113958,114231,114375,114576,114623,114679,157170,157909,165023,165744,170869,176380,178685,178967,179272,179534,179794,181217,183762,189718,190286"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/c3e60d8edb6a86f432de0791fcaa87bd/transformed/lifecycle-runtime-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "347", "startColumns": "4", "startOffsets": "20568", "endColumns": "42", "endOffsets": "20606"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/6bf7b9bec3a755c3e68569d2ddbec360/transformed/browser-1.8.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "82,83,84,85,213,214,395,397,398,399", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3726,3784,3850,3913,12493,12564,23776,23901,23968,24047", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3779,3845,3908,3970,12559,12631,23839,23963,24042,24111"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/a6152668d1a27e461f944bc1fd9e12ca/transformed/coordinatorlayout-1.0.0/res/values/values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2086,2796,2802", "startColumns": "4,4,4,4", "startOffsets": "164,135793,159572,159783", "endLines": "3,2088,2801,2885", "endColumns": "60,12,24,24", "endOffsets": "220,135933,159778,164294"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/179d597149d8b44255724080d49dad56/transformed/jetified-appcompat-resources-1.1.0/res/values/values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2214,2230,2236,3524,3540", "startColumns": "4,4,4,4,4", "startOffsets": "140331,140756,140934,185629,186040", "endLines": "2229,2235,2245,3539,3543", "endColumns": "24,24,24,24,24", "endOffsets": "140751,140929,141213,186035,186162"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/a017c0c1c150e766625df730ca1d1522/transformed/jetified-window-1.2.0/res/values/values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,306,2193,2199,3485,3493,3508", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,18486,139461,139656,184079,184361,184975", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,306,2198,2203,3492,3507,3523", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,18541,139651,139809,184356,184970,185624"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/4346eeaa665b3533cb1f34b1539bb5b7/transformed/recyclerview-1.0.0/res/values/values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "224,225,226,234,235,236,311,3405", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "13297,13356,13404,14071,14146,14222,18724,181437", "endLines": "224,225,226,234,235,236,311,3424", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "13351,13399,13455,14141,14217,14289,18785,182227"}}]}]}