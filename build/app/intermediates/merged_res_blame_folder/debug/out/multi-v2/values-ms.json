{"logs": [{"outputFile": "com.technews.tech_news_app-mergeDebugResources-41:/values-ms/values-ms.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/799d1c16666fc18cb929ebf700d90383/transformed/appcompat-1.1.0/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,889,980,1072,1167,1261,1360,1453,1548,1642,1733,1824,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,78,90,91,94,93,98,92,94,93,90,90,78,111,107,96,108,103,106,158,100,79", "endOffsets": "211,316,424,511,615,726,805,884,975,1067,1162,1256,1355,1448,1543,1637,1728,1819,1898,2010,2118,2215,2324,2428,2535,2694,2795,2875"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,889,980,1072,1167,1261,1360,1453,1548,1642,1733,1824,1903,2015,2123,2220,2329,2433,2540,2699,4349", "endColumns": "110,104,107,86,103,110,78,78,90,91,94,93,98,92,94,93,90,90,78,111,107,96,108,103,106,158,100,79", "endOffsets": "211,316,424,511,615,726,805,884,975,1067,1162,1256,1355,1448,1543,1637,1728,1819,1898,2010,2118,2215,2324,2428,2535,2694,2795,4424"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/6bf7b9bec3a755c3e68569d2ddbec360/transformed/browser-1.8.0/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,379", "endColumns": "104,99,118,101", "endOffsets": "155,255,374,476"}, "to": {"startLines": "37,39,40,41", "startColumns": "4,4,4,4", "startOffsets": "3612,3806,3906,4025", "endColumns": "104,99,118,101", "endOffsets": "3712,3901,4020,4122"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/1c848f8f37c5e484fe559c7671e5ae91/transformed/preference-1.2.1/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,263,346,485,654,735", "endColumns": "68,88,82,138,168,80,78", "endOffsets": "169,258,341,480,649,730,809"}, "to": {"startLines": "36,38,42,43,46,47,48", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3543,3717,4127,4210,4530,4699,4780", "endColumns": "68,88,82,138,168,80,78", "endOffsets": "3607,3801,4205,4344,4694,4775,4854"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/28ee8a5ebc96d117de5598ee6fce01ba/transformed/core-1.13.1/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "29,30,31,32,33,34,35,45", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2800,2895,2997,3094,3204,3310,3428,4429", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "2890,2992,3089,3199,3305,3423,3538,4525"}}]}]}