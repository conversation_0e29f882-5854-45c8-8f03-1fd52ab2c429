{"logs": [{"outputFile": "com.technews.tech_news_app-mergeDebugResources-41:/values-pt/values-pt.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/1c848f8f37c5e484fe559c7671e5ae91/transformed/preference-1.2.1/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,341,492,661,748", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "170,257,336,487,656,743,824"}, "to": {"startLines": "36,38,42,43,46,47,48", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3578,3763,4167,4246,4583,4752,4839", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "3643,3845,4241,4392,4747,4834,4915"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/799d1c16666fc18cb929ebf700d90383/transformed/appcompat-1.1.0/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,913,1004,1096,1191,1285,1386,1479,1574,1669,1760,1851,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,80,90,91,94,93,100,92,94,94,90,90,83,106,110,101,107,107,109,161,99,84", "endOffsets": "220,326,433,522,623,742,827,908,999,1091,1186,1280,1381,1474,1569,1664,1755,1846,1930,2037,2148,2250,2358,2466,2576,2738,2838,2923"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,913,1004,1096,1191,1285,1386,1479,1574,1669,1760,1851,1935,2042,2153,2255,2363,2471,2581,2743,4397", "endColumns": "119,105,106,88,100,118,84,80,90,91,94,93,100,92,94,94,90,90,83,106,110,101,107,107,109,161,99,84", "endOffsets": "220,326,433,522,623,742,827,908,999,1091,1186,1280,1381,1474,1569,1664,1755,1846,1930,2037,2148,2250,2358,2466,2576,2738,2838,4477"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/6bf7b9bec3a755c3e68569d2ddbec360/transformed/browser-1.8.0/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "37,39,40,41", "startColumns": "4,4,4,4", "startOffsets": "3648,3850,3949,4061", "endColumns": "114,98,111,105", "endOffsets": "3758,3944,4056,4162"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/28ee8a5ebc96d117de5598ee6fce01ba/transformed/core-1.13.1/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "29,30,31,32,33,34,35,45", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2843,2940,3042,3141,3241,3348,3458,4482", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "2935,3037,3136,3236,3343,3453,3573,4578"}}]}]}