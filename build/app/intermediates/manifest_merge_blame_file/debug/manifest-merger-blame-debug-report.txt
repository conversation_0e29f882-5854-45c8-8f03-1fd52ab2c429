1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.technews.tech_news_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/Documents/augment-projects/tech-news-flutter/android/app/src/main/AndroidManifest.xml:3:5-67
15-->/Users/<USER>/Documents/augment-projects/tech-news-flutter/android/app/src/main/AndroidManifest.xml:3:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->/Users/<USER>/Documents/augment-projects/tech-news-flutter/android/app/src/main/AndroidManifest.xml:4:5-79
16-->/Users/<USER>/Documents/augment-projects/tech-news-flutter/android/app/src/main/AndroidManifest.xml:4:22-76
17    <!--
18 Required to query activities that can process text, see:
19         https://developer.android.com/training/package-visibility and
20         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
21
22         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
23    -->
24    <queries>
24-->/Users/<USER>/Documents/augment-projects/tech-news-flutter/android/app/src/main/AndroidManifest.xml:43:5-48:15
25        <intent>
25-->/Users/<USER>/Documents/augment-projects/tech-news-flutter/android/app/src/main/AndroidManifest.xml:44:9-47:18
26            <action android:name="android.intent.action.PROCESS_TEXT" />
26-->/Users/<USER>/Documents/augment-projects/tech-news-flutter/android/app/src/main/AndroidManifest.xml:45:13-72
26-->/Users/<USER>/Documents/augment-projects/tech-news-flutter/android/app/src/main/AndroidManifest.xml:45:21-70
27
28            <data android:mimeType="text/plain" />
28-->/Users/<USER>/Documents/augment-projects/tech-news-flutter/android/app/src/main/AndroidManifest.xml:46:13-50
28-->/Users/<USER>/Documents/augment-projects/tech-news-flutter/android/app/src/main/AndroidManifest.xml:46:19-48
29        </intent>
30    </queries>
31
32    <permission
32-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/28ee8a5ebc96d117de5598ee6fce01ba/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
33        android:name="com.technews.tech_news_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
33-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/28ee8a5ebc96d117de5598ee6fce01ba/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
34        android:protectionLevel="signature" />
34-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/28ee8a5ebc96d117de5598ee6fce01ba/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
35
36    <uses-permission android:name="com.technews.tech_news_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
36-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/28ee8a5ebc96d117de5598ee6fce01ba/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
36-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/28ee8a5ebc96d117de5598ee6fce01ba/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
37
38    <application
39        android:name="android.app.Application"
40        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
40-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/28ee8a5ebc96d117de5598ee6fce01ba/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
41        android:debuggable="true"
42        android:extractNativeLibs="true"
43        android:icon="@mipmap/ic_launcher"
44        android:label="tech_news_app" >
45        <activity
46            android:name="com.technews.tech_news_app.MainActivity"
47            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
48            android:exported="true"
49            android:hardwareAccelerated="true"
50            android:launchMode="singleTop"
51            android:taskAffinity=""
52            android:theme="@style/LaunchTheme"
53            android:windowSoftInputMode="adjustResize" >
54
55            <!--
56                 Specifies an Android theme to apply to this Activity as soon as
57                 the Android process has started. This theme is visible to the user
58                 while the Flutter UI initializes. After that, this theme continues
59                 to determine the Window background behind the Flutter UI.
60            -->
61            <meta-data
62                android:name="io.flutter.embedding.android.NormalTheme"
63                android:resource="@style/NormalTheme" />
64
65            <intent-filter>
66                <action android:name="android.intent.action.MAIN" />
67
68                <category android:name="android.intent.category.LAUNCHER" />
69            </intent-filter>
70        </activity>
71        <!--
72             Don't delete the meta-data below.
73             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
74        -->
75        <meta-data
76            android:name="flutterEmbedding"
77            android:value="2" />
78
79        <uses-library
79-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/a017c0c1c150e766625df730ca1d1522/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
80            android:name="androidx.window.extensions"
80-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/a017c0c1c150e766625df730ca1d1522/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
81            android:required="false" />
81-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/a017c0c1c150e766625df730ca1d1522/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
82        <uses-library
82-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/a017c0c1c150e766625df730ca1d1522/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
83            android:name="androidx.window.sidecar"
83-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/a017c0c1c150e766625df730ca1d1522/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
84            android:required="false" />
84-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/a017c0c1c150e766625df730ca1d1522/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
85
86        <provider
86-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/6f1de229824e3b626519c3b4ed1eee9b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
87            android:name="androidx.startup.InitializationProvider"
87-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/6f1de229824e3b626519c3b4ed1eee9b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
88            android:authorities="com.technews.tech_news_app.androidx-startup"
88-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/6f1de229824e3b626519c3b4ed1eee9b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
89            android:exported="false" >
89-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/6f1de229824e3b626519c3b4ed1eee9b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
90            <meta-data
90-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/6f1de229824e3b626519c3b4ed1eee9b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
91                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
91-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/6f1de229824e3b626519c3b4ed1eee9b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
92                android:value="androidx.startup" />
92-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/6f1de229824e3b626519c3b4ed1eee9b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
93            <meta-data
93-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
94                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
94-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
95                android:value="androidx.startup" />
95-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
96        </provider>
97
98        <receiver
98-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
99            android:name="androidx.profileinstaller.ProfileInstallReceiver"
99-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
100            android:directBootAware="false"
100-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
101            android:enabled="true"
101-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
102            android:exported="true"
102-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
103            android:permission="android.permission.DUMP" >
103-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
104            <intent-filter>
104-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
105                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
105-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
105-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
106            </intent-filter>
107            <intent-filter>
107-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
108                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
108-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
108-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
109            </intent-filter>
110            <intent-filter>
110-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
111                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
111-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
111-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
112            </intent-filter>
113            <intent-filter>
113-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
114                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
114-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
114-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/497b559c30d96929b625a427f3dee739/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
115            </intent-filter>
116        </receiver>
117    </application>
118
119</manifest>
