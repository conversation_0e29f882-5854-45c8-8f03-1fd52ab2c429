_fe_analyzer_shared
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-72.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-72.0.0/lib/
analyzer
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.7.0/lib/
ansicolor
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ansicolor-2.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ansicolor-2.0.3/lib/
archive
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/
args
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/lib/
async
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/lib/
boolean_selector
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/lib/
build
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build-2.4.1/lib/
build_config
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.1/lib/
build_daemon
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.2/lib/
build_resolvers
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.4.2/lib/
build_runner
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.4.13/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.4.13/lib/
build_runner_core
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-7.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-7.3.2/lib/
built_collection
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/lib/
built_value
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.10.1/lib/
characters
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/
checked_yaml
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/lib/
clock
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/lib/
code_builder
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/lib/
collection
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/
convert
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/lib/
crypto
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/
csslib
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/
cupertino_icons
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/lib/
dart_style
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.7/lib/
dartz
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/
equatable
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/
fake_async
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.1/lib/
ffi
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/
file
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/
fixnum
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/
flutter_lints
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-4.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-4.0.0/lib/
flutter_native_splash
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.4/lib/
frontend_server_client
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/lib/
glob
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/lib/
graphs
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/lib/
html
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/
http_multi_server
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/lib/
http_parser
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/
image
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/
io
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/lib/
js
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.7.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.7.1/lib/
json_annotation
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/
leak_tracker
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.5/lib/
leak_tracker_flutter_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.5/lib/
leak_tracker_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
lints
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-4.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-4.0.0/lib/
logging
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/
macros
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/macros-0.1.2-main.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/macros-0.1.2-main.4/lib/
matcher
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.16+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.16+1/lib/
material_color_utilities
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/
meta
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.15.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.15.0/lib/
mime
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/
mockito
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mockito-5.4.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mockito-5.4.4/lib/
nested
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/lib/
package_config
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/lib/
path
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/
path_provider_linux
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/
petitparser
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/lib/
platform
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
pool
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/lib/
posix
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.2/lib/
provider
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/
pub_semver
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/lib/
pubspec_parse
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.4.0/lib/
shared_preferences
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/
shared_preferences_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.7/lib/
shared_preferences_foundation
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/
shared_preferences_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib/
shared_preferences_windows
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
shelf
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.1/lib/
shelf_web_socket
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-2.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-2.0.1/lib/
source_gen
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0/lib/
source_span
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib/
stack_trace
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/lib/
stream_channel
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/lib/
stream_transform
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/
string_scanner
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib/
term_glyph
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib/
test_api
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.2/lib/
timing
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/lib/
typed_data
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/
universal_io
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/universal_io-2.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/universal_io-2.2.2/lib/
vector_math
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/
vm_service
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.2.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.2.5/lib/
watcher
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.2/lib/
web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/
web_socket
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib/
web_socket_channel
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib/
webview_flutter
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.10.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.10.0/lib/
webview_flutter_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.3.2/lib/
webview_flutter_platform_interface
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.0/lib/
webview_flutter_wkwebview
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/lib/
xdg_directories
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/
xml
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/
yaml
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/lib/
tech_news_app
3.5
file:///Users/<USER>/Documents/augment-projects/tech-news-flutter/
file:///Users/<USER>/Documents/augment-projects/tech-news-flutter/lib/
_macros
3.4
file:///opt/homebrew/Caskroom/flutter/3.10.0/flutter/bin/cache/dart-sdk/pkg/_macros/
file:///opt/homebrew/Caskroom/flutter/3.10.0/flutter/bin/cache/dart-sdk/pkg/_macros/lib/
sky_engine
3.2
file:///opt/homebrew/Caskroom/flutter/3.10.0/flutter/bin/cache/pkg/sky_engine/
file:///opt/homebrew/Caskroom/flutter/3.10.0/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.3
file:///opt/homebrew/Caskroom/flutter/3.10.0/flutter/packages/flutter/
file:///opt/homebrew/Caskroom/flutter/3.10.0/flutter/packages/flutter/lib/
flutter_test
3.3
file:///opt/homebrew/Caskroom/flutter/3.10.0/flutter/packages/flutter_test/
file:///opt/homebrew/Caskroom/flutter/3.10.0/flutter/packages/flutter_test/lib/
flutter_web_plugins
3.2
file:///opt/homebrew/Caskroom/flutter/3.10.0/flutter/packages/flutter_web_plugins/
file:///opt/homebrew/Caskroom/flutter/3.10.0/flutter/packages/flutter_web_plugins/lib/
2
