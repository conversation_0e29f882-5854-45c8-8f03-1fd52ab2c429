{"inputs": ["/opt/homebrew/Caskroom/flutter/3.10.0/flutter/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "/Users/<USER>/Documents/augment-projects/tech-news-flutter/.dart_tool/package_config_subset"], "outputs": ["/Users/<USER>/Documents/augment-projects/tech-news-flutter/.dart_tool/flutter_build/6670ad79a024a3f7d1d7b94dd95d3586/native_assets.yaml", "/Users/<USER>/Documents/augment-projects/tech-news-flutter/.dart_tool/flutter_build/6670ad79a024a3f7d1d7b94dd95d3586/native_assets.yaml"]}