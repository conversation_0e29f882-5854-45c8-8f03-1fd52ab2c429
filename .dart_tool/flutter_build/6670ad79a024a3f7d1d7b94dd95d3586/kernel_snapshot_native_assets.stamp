{"inputs": ["/Users/<USER>/Documents/augment-projects/tech-news-flutter/.dart_tool/flutter_build/6670ad79a024a3f7d1d7b94dd95d3586/native_assets.yaml", "/Users/<USER>/Documents/augment-projects/tech-news-flutter/.dart_tool/package_config_subset", "/opt/homebrew/Caskroom/flutter/3.10.0/flutter/packages/flutter_tools/lib/src/build_system/targets/common.dart", "/opt/homebrew/Caskroom/flutter/3.10.0/flutter/bin/internal/engine.version", "/opt/homebrew/Caskroom/flutter/3.10.0/flutter/bin/internal/engine.version", "/opt/homebrew/Caskroom/flutter/3.10.0/flutter/bin/internal/engine.version", "/opt/homebrew/Caskroom/flutter/3.10.0/flutter/bin/internal/engine.version"], "outputs": ["/Users/<USER>/Documents/augment-projects/tech-news-flutter/.dart_tool/flutter_build/6670ad79a024a3f7d1d7b94dd95d3586/native_assets.dill"]}