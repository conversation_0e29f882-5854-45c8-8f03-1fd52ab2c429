/// Abstract class for network information
abstract class NetworkInfo {
  Future<bool> get isConnected;
}

/// Simple implementation of NetworkInfo (assumes connected)
class NetworkInfoImpl implements NetworkInfo {
  NetworkInfoImpl();

  @override
  Future<bool> get isConnected async {
    // For simplicity, assume always connected
    // In a real app, you could implement actual network checking
    return true;
  }
}
