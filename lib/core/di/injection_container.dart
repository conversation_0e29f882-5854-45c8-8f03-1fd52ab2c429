import 'package:provider/provider.dart';
import 'package:flutter/material.dart';

// Core
import '../utils/network_info.dart';

// Features - News
import '../../features/news/data/datasources/local_data_source.dart';
import '../../features/news/data/repositories/news_repository_impl.dart';
import '../../features/news/domain/usecases/get_news_sources.dart';
import '../../features/news/domain/usecases/manage_bookmarks.dart';
import '../../features/news/presentation/providers/news_provider.dart';
import '../../features/news/presentation/providers/webview_provider.dart';


// Shared Services
import '../../shared/services/connectivity_service.dart';
import '../../shared/services/theme_service.dart';

/// Dependency injection container for the app
class InjectionContainer {
  static late ConnectivityService _connectivityService;
  static late ThemeService _themeService;
  static late NewsProvider _newsProvider;
  static late WebViewProvider _webViewProvider;

  /// Initialize all dependencies
  static Future<void> init() async {
    // Core
    final networkInfo = NetworkInfoImpl();

    // Services
    _connectivityService = ConnectivityService();
    await _connectivityService.initialize();

    _themeService = ThemeService();

    // Data sources
    final localDataSource = LocalDataSourceImpl();
    
    // Repositories
    final newsRepository = NewsRepositoryImpl(
      localDataSource: localDataSource,
      networkInfo: networkInfo,
    );
    
    // Use cases
    final getNewsSources = GetNewsSources(newsRepository);
    final getBookmarks = GetBookmarks(newsRepository);
    final addBookmark = AddBookmark(newsRepository);
    final removeBookmark = RemoveBookmark(newsRepository);
    final isBookmarked = IsBookmarked(newsRepository);
    
    // Providers
    _newsProvider = NewsProvider(
      getNewsSources: getNewsSources,
      getBookmarks: getBookmarks,
      addBookmark: addBookmark,
      removeBookmark: removeBookmark,
      isBookmarked: isBookmarked,
      repository: newsRepository,
    );
    
    _webViewProvider = WebViewProvider();

    // Initialize providers
    await _newsProvider.initialize();
  }

  /// Get MultiProvider with all providers
  static MultiProvider getMultiProvider({required Widget child}) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<ConnectivityService>.value(
          value: _connectivityService,
        ),
        ChangeNotifierProvider<ThemeService>.value(
          value: _themeService,
        ),
        ChangeNotifierProvider<NewsProvider>.value(
          value: _newsProvider,
        ),
        ChangeNotifierProvider<WebViewProvider>.value(
          value: _webViewProvider,
        ),
      ],
      child: child,
    );
  }

  /// Get individual services (for testing or specific use cases)
  static ConnectivityService get connectivityService => _connectivityService;
  static ThemeService get themeService => _themeService;
  static NewsProvider get newsProvider => _newsProvider;
  static WebViewProvider get webViewProvider => _webViewProvider;
}
