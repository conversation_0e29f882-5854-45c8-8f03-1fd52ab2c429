/// Application constants for the Tech News App
class AppConstants {
  // App Information
  static const String appName = 'Tech News';
  static const String appVersion = '1.0.0';
  
  // Default News Sources
  static const String defaultNewsUrl = 'https://techcrunch.com';
  static const List<String> techNewsSources = [
    'https://techcrunch.com',
    'https://www.theverge.com',
    'https://arstechnica.com',
    'https://www.wired.com',
    'https://www.engadget.com',
    'https://www.cnet.com',
    'https://www.zdnet.com',
    'https://venturebeat.com',
    'https://mashable.com',
    'https://gizmodo.com',
  ];
  
  // WebView Configuration
  static const String userAgent = 'TechNewsApp/1.0.0 (Flutter WebView)';
  static const int webViewTimeoutSeconds = 30;
  
  // Storage Keys
  static const String keySelectedNewsSource = 'selected_news_source';
  static const String keyThemeMode = 'theme_mode';
  static const String keyFirstLaunch = 'first_launch';
  static const String keyBookmarks = 'bookmarks';
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  
  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 400);
  static const Duration longAnimationDuration = Duration(milliseconds: 600);
  
  // Error Messages
  static const String networkErrorMessage = 'No internet connection. Please check your network settings.';
  static const String genericErrorMessage = 'Something went wrong. Please try again.';
  static const String webViewErrorMessage = 'Failed to load the webpage. Please try again.';
  
  // Success Messages
  static const String bookmarkAddedMessage = 'Bookmark added successfully';
  static const String bookmarkRemovedMessage = 'Bookmark removed successfully';
}

/// URL validation and utility constants
class UrlConstants {
  static const String httpPrefix = 'http://';
  static const String httpsPrefix = 'https://';
  
  static bool isValidUrl(String url) {
    return Uri.tryParse(url) != null && 
           (url.startsWith(httpPrefix) || url.startsWith(httpsPrefix));
  }
  
  static String ensureHttps(String url) {
    if (!url.startsWith(httpPrefix) && !url.startsWith(httpsPrefix)) {
      return httpsPrefix + url;
    }
    return url;
  }
}
