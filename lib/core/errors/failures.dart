import 'package:equatable/equatable.dart';

/// Base class for all failures in the application
abstract class Failure extends Equatable {
  final String message;
  
  const Failure(this.message);
  
  @override
  List<Object> get props => [message];
}

/// Network-related failures
class NetworkFailure extends Failure {
  const NetworkFailure(super.message);
}

/// Server-related failures
class ServerFailure extends Failure {
  const ServerFailure(super.message);
}

/// Cache-related failures
class CacheFailure extends Failure {
  const CacheFailure(super.message);
}

/// WebView-related failures
class WebViewFailure extends Failure {
  const WebViewFailure(super.message);
}

/// General application failures
class GeneralFailure extends Failure {
  const GeneralFailure(super.message);
}

/// URL validation failures
class UrlValidationFailure extends Failure {
  const UrlValidationFailure(super.message);
}
