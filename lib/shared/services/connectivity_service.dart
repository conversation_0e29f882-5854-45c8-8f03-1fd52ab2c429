import 'package:flutter/foundation.dart';

/// Simple service for connectivity status (assumes always connected)
class ConnectivityService extends ChangeNotifier {
  bool _isConnected = true;
  bool _isInitialized = false;

  bool get isConnected => _isConnected;
  bool get isInitialized => _isInitialized;

  /// Initialize the connectivity service
  Future<void> initialize() async {
    if (_isInitialized) return;

    _isConnected = true;
    _isInitialized = true;

    debugPrint('Connectivity service initialized (always connected)');
  }

  /// Get connectivity information
  Future<Map<String, dynamic>> getConnectivityInfo() async {
    return {
      'isConnected': _isConnected,
      'connectionTypes': ['wifi'], // Assume wifi for simplicity
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}
