import 'package:equatable/equatable.dart';

/// Bookmark entity representing a saved news article or page
class Bookmark extends Equatable {
  final String id;
  final String title;
  final String url;
  final String description;
  final DateTime createdAt;
  final String? imageUrl;
  final String? sourceName;

  const Bookmark({
    required this.id,
    required this.title,
    required this.url,
    required this.description,
    required this.createdAt,
    this.imageUrl,
    this.sourceName,
  });

  /// Create a copy of this bookmark with updated fields
  Bookmark copyWith({
    String? id,
    String? title,
    String? url,
    String? description,
    DateTime? createdAt,
    String? imageUrl,
    String? sourceName,
  }) {
    return Bookmark(
      id: id ?? this.id,
      title: title ?? this.title,
      url: url ?? this.url,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      imageUrl: imageUrl ?? this.imageUrl,
      sourceName: sourceName ?? this.sourceName,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        url,
        description,
        createdAt,
        imageUrl,
        sourceName,
      ];

  @override
  String toString() {
    return 'Bookmark(id: $id, title: $title, url: $url, description: $description, createdAt: $createdAt, imageUrl: $imageUrl, sourceName: $sourceName)';
  }
}
