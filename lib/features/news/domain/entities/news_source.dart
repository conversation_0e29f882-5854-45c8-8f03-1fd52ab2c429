import 'package:equatable/equatable.dart';

/// News source entity representing a tech news website
class NewsSource extends Equatable {
  final String id;
  final String name;
  final String url;
  final String description;
  final String iconUrl;
  final bool isActive;

  const NewsSource({
    required this.id,
    required this.name,
    required this.url,
    required this.description,
    this.iconUrl = '',
    this.isActive = true,
  });

  /// Create a copy of this news source with updated fields
  NewsSource copyWith({
    String? id,
    String? name,
    String? url,
    String? description,
    String? iconUrl,
    bool? isActive,
  }) {
    return NewsSource(
      id: id ?? this.id,
      name: name ?? this.name,
      url: url ?? this.url,
      description: description ?? this.description,
      iconUrl: iconUrl ?? this.iconUrl,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  List<Object> get props => [id, name, url, description, iconUrl, isActive];

  @override
  String toString() {
    return 'NewsSource(id: $id, name: $name, url: $url, description: $description, iconUrl: $iconUrl, isActive: $isActive)';
  }
}
