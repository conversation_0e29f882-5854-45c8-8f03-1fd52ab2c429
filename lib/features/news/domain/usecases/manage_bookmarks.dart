import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/bookmark.dart';
import '../repositories/news_repository.dart';

/// Use case for getting all bookmarks
class GetBookmarks {
  final NewsRepository repository;

  GetBookmarks(this.repository);

  Future<Either<Failure, List<Bookmark>>> call() async {
    return await repository.getBookmarks();
  }
}

/// Use case for adding a bookmark
class AddBookmark {
  final NewsRepository repository;

  AddBookmark(this.repository);

  Future<Either<Failure, void>> call(Bookmark bookmark) async {
    return await repository.addBookmark(bookmark);
  }
}

/// Use case for removing a bookmark
class RemoveBookmark {
  final NewsRepository repository;

  RemoveBookmark(this.repository);

  Future<Either<Failure, void>> call(String bookmarkId) async {
    return await repository.removeBookmark(bookmarkId);
  }
}

/// Use case for checking if a URL is bookmarked
class IsBookmarked {
  final NewsRepository repository;

  IsBookmarked(this.repository);

  Future<Either<Failure, bool>> call(String url) async {
    return await repository.isBookmarked(url);
  }
}
