import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../shared/services/connectivity_service.dart';
import '../providers/webview_provider.dart';

/// WebView page for displaying news content
class WebViewPage extends StatefulWidget {
  const WebViewPage({super.key});

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeWebView();
    });
  }

  void _initializeWebView() {
    final webViewProvider = Provider.of<WebViewProvider>(context, listen: false);
    // Always load Tech News IO
    webViewProvider.initializeController(AppConstants.techNewsUrl);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Consumer2<WebViewProvider, ConnectivityService>(
      builder: (context, webViewProvider, connectivityService, child) {
        // Show error if no internet connection
        if (!connectivityService.isConnected) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.wifi_off, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  AppConstants.networkErrorMessage,
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16),
                ),
              ],
            ),
          );
        }

        // Show loading if WebView is not initialized
        if (webViewProvider.controller == null) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Initializing...'),
              ],
            ),
          );
        }

        return Column(
          children: [
            // Simple loading progress indicator
            if (webViewProvider.isLoading && webViewProvider.loadingProgress < 100)
              LinearProgressIndicator(
                value: webViewProvider.loadingProgress / 100,
                backgroundColor: Colors.grey[300],
              ),

            // WebView content
            Expanded(
              child: Stack(
                children: [
                  WebViewWidget(controller: webViewProvider.controller!),

                  // Error overlay
                  if (webViewProvider.errorMessage != null)
                    Container(
                      color: Colors.white,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.error_outline, size: 64, color: Colors.red),
                            const SizedBox(height: 16),
                            Text(
                              webViewProvider.errorMessage!,
                              textAlign: TextAlign.center,
                              style: const TextStyle(fontSize: 16),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: webViewProvider.reload,
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      ),
                    ),

                  // Loading overlay for initial load
                  if (webViewProvider.isLoading && webViewProvider.loadingProgress == 0)
                    Container(
                      color: Colors.white,
                      child: const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text('Loading Tech News...'),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }


}


