import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../shared/services/connectivity_service.dart';
import '../../domain/entities/bookmark.dart';
import '../providers/news_provider.dart';
import '../providers/webview_provider.dart';
import '../widgets/webview_app_bar.dart';
import '../widgets/loading_indicator.dart';
import '../widgets/error_widget.dart';

/// WebView page for displaying news content
class WebViewPage extends StatefulWidget {
  const WebViewPage({super.key});

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeWebView();
    });
  }

  void _initializeWebView() {
    final newsProvider = Provider.of<NewsProvider>(context, listen: false);
    final webViewProvider = Provider.of<WebViewProvider>(context, listen: false);
    
    final selectedSource = newsProvider.selectedNewsSource;
    if (selectedSource != null) {
      webViewProvider.initializeController(selectedSource.url);
    } else {
      webViewProvider.initializeController(AppConstants.defaultNewsUrl);
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Consumer3<WebViewProvider, NewsProvider, ConnectivityService>(
      builder: (context, webViewProvider, newsProvider, connectivityService, child) {
        // Show error if no internet connection
        if (!connectivityService.isConnected) {
          return const Center(
            child: CustomErrorWidget(
              message: AppConstants.networkErrorMessage,
              icon: Icons.wifi_off,
            ),
          );
        }

        // Show loading if WebView is not initialized
        if (webViewProvider.controller == null) {
          return const Center(
            child: LoadingIndicator(message: 'Initializing...'),
          );
        }

        return Column(
          children: [
            // App bar with navigation controls
            WebViewAppBar(
              title: webViewProvider.currentTitle.isNotEmpty 
                  ? webViewProvider.currentTitle 
                  : newsProvider.selectedNewsSource?.name ?? 'Tech News',
              canGoBack: webViewProvider.canGoBack,
              canGoForward: webViewProvider.canGoForward,
              onBackPressed: webViewProvider.goBack,
              onForwardPressed: webViewProvider.goForward,
              onRefreshPressed: webViewProvider.reload,
              onSharePressed: () => _shareCurrentUrl(webViewProvider.currentUrl),
              onBookmarkPressed: () => _toggleBookmark(context, webViewProvider),
            ),
            
            // Loading progress indicator
            if (webViewProvider.isLoading && webViewProvider.loadingProgress < 100)
              LinearProgressIndicator(
                value: webViewProvider.loadingProgress / 100,
                backgroundColor: Colors.grey[300],
              ),
            
            // WebView content
            Expanded(
              child: Stack(
                children: [
                  WebViewWidget(controller: webViewProvider.controller!),
                  
                  // Error overlay
                  if (webViewProvider.errorMessage != null)
                    Container(
                      color: Colors.white,
                      child: Center(
                        child: CustomErrorWidget(
                          message: webViewProvider.errorMessage!,
                          onRetry: webViewProvider.reload,
                        ),
                      ),
                    ),
                  
                  // Loading overlay
                  if (webViewProvider.isLoading)
                    Container(
                      color: Colors.white.withOpacity(0.8),
                      child: const Center(
                        child: LoadingIndicator(message: 'Loading...'),
                      ),
                    ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _shareCurrentUrl(String url) async {
    if (url.isNotEmpty) {
      // In a real app, you would use share_plus package
      // For now, we'll just copy to clipboard or open in browser
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    }
  }

  Future<void> _toggleBookmark(BuildContext context, WebViewProvider webViewProvider) async {
    final newsProvider = Provider.of<NewsProvider>(context, listen: false);
    final currentUrl = webViewProvider.currentUrl;
    final currentTitle = webViewProvider.currentTitle;
    
    if (currentUrl.isEmpty) return;
    
    final isBookmarked = await newsProvider.checkIsBookmarked(currentUrl);
    
    if (isBookmarked) {
      // Remove bookmark
      final bookmark = newsProvider.getBookmarkByUrl(currentUrl);
      if (bookmark != null) {
        final success = await newsProvider.removeExistingBookmark(bookmark.id);
        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text(AppConstants.bookmarkRemovedMessage)),
          );
        }
      }
    } else {
      // Add bookmark
      final bookmark = Bookmark(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: currentTitle.isNotEmpty ? currentTitle : 'Untitled',
        url: currentUrl,
        description: 'Bookmarked from ${newsProvider.selectedNewsSource?.name ?? 'Tech News'}',
        createdAt: DateTime.now(),
        sourceName: newsProvider.selectedNewsSource?.name,
      );
      
      final success = await newsProvider.addNewBookmark(bookmark);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text(AppConstants.bookmarkAddedMessage)),
        );
      }
    }
  }
}


