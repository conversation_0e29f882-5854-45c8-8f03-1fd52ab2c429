import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../shared/services/connectivity_service.dart';
import '../../domain/entities/bookmark.dart';
import '../providers/news_provider.dart';
import '../providers/webview_provider.dart';

/// WebView page for displaying tech-news.io content
class WebViewPage extends StatefulWidget {
  const WebViewPage({super.key});

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeWebView();
    });
  }

  void _initializeWebView() {
    final webViewProvider = Provider.of<WebViewProvider>(context, listen: false);
    // Always load Tech News IO
    webViewProvider.initializeController(AppConstants.techNewsUrl);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Consumer3<WebViewProvider, NewsProvider, ConnectivityService>(
      builder: (context, webViewProvider, newsProvider, connectivityService, child) {
        // Show error if no internet connection
        if (!connectivityService.isConnected) {
          return _buildErrorState(
            icon: Icons.wifi_off,
            title: 'No Internet Connection',
            message: AppConstants.networkErrorMessage,
            onRetry: () => _initializeWebView(),
          );
        }

        // Show loading if WebView is not initialized
        if (webViewProvider.controller == null) {
          return _buildLoadingState('Initializing WebView...');
        }

        return RefreshIndicator(
          onRefresh: () async {
            await webViewProvider.reload();
            // Show a brief success message
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Page refreshed'),
                  duration: Duration(seconds: 1),
                ),
              );
            }
          },
          color: Theme.of(context).primaryColor,
          child: Column(
            children: [
              // WebView App Bar
              _buildWebViewAppBar(context, webViewProvider, newsProvider),

              // Loading progress indicator
              if (webViewProvider.isLoading && webViewProvider.loadingProgress < 100)
                LinearProgressIndicator(
                  value: webViewProvider.loadingProgress / 100,
                  backgroundColor: Colors.grey[300],
                ),

              // WebView content
              Expanded(
                child: Stack(
                  children: [
                    WebViewWidget(controller: webViewProvider.controller!),

                    // Error overlay
                    if (webViewProvider.errorMessage != null)
                      _buildErrorOverlay(webViewProvider),

                    // Loading overlay for initial load
                    if (webViewProvider.isLoading && webViewProvider.loadingProgress == 0)
                      _buildLoadingOverlay(),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildWebViewAppBar(BuildContext context, WebViewProvider webViewProvider, NewsProvider newsProvider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: Theme.of(context).appBarTheme.backgroundColor,
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: webViewProvider.canGoBack ? webViewProvider.goBack : null,
            icon: const Icon(Icons.arrow_back_ios, size: 20),
            tooltip: 'Go Back',
          ),

          // Forward button
          IconButton(
            onPressed: webViewProvider.canGoForward ? webViewProvider.goForward : null,
            icon: const Icon(Icons.arrow_forward_ios, size: 20),
            tooltip: 'Go Forward',
          ),

          // Refresh button
          IconButton(
            onPressed: webViewProvider.reload,
            icon: const Icon(Icons.refresh, size: 20),
            tooltip: 'Refresh',
          ),

          const Spacer(),

          // Bookmark button
          IconButton(
            onPressed: () => _toggleBookmark(context, webViewProvider, newsProvider),
            icon: const Icon(Icons.bookmark_add, size: 20),
            tooltip: 'Add Bookmark',
          ),

          // Share button
          IconButton(
            onPressed: () => _shareCurrentUrl(webViewProvider.currentUrl),
            icon: const Icon(Icons.share, size: 20),
            tooltip: 'Share',
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            message,
            style: const TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState({
    required IconData icon,
    required String title,
    required String message,
    VoidCallback? onRetry,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildErrorOverlay(WebViewProvider webViewProvider) {
    return Container(
      color: Colors.white,
      child: _buildErrorState(
        icon: Icons.error_outline,
        title: 'Failed to Load Page',
        message: webViewProvider.errorMessage!,
        onRetry: webViewProvider.reload,
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.white.withOpacity(0.9),
      child: _buildLoadingState('Loading Tech News...'),
    );
  }

  Future<void> _shareCurrentUrl(String url) async {
    if (url.isNotEmpty) {
      try {
        await launchUrl(
          Uri.parse(url),
          mode: LaunchMode.externalApplication,
        );
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to share: ${e.toString()}')),
          );
        }
      }
    }
  }

  Future<void> _toggleBookmark(BuildContext context, WebViewProvider webViewProvider, NewsProvider newsProvider) async {
    final currentUrl = webViewProvider.currentUrl;
    final currentTitle = webViewProvider.currentTitle;

    if (currentUrl.isEmpty) return;

    final isBookmarked = await newsProvider.checkIsBookmarked(currentUrl);

    if (isBookmarked) {
      // Remove bookmark
      final bookmark = newsProvider.getBookmarkByUrl(currentUrl);
      if (bookmark != null) {
        final success = await newsProvider.removeExistingBookmark(bookmark.id);
        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text(AppConstants.bookmarkRemovedMessage)),
          );
        }
      }
    } else {
      // Add bookmark
      final bookmark = Bookmark(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: currentTitle.isNotEmpty ? currentTitle : 'Tech News Article',
        url: currentUrl,
        description: 'Bookmarked from ${AppConstants.techNewsName}',
        createdAt: DateTime.now(),
        sourceName: AppConstants.techNewsName,
      );

      final success = await newsProvider.addNewBookmark(bookmark);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text(AppConstants.bookmarkAddedMessage)),
        );
      }
    }
  }
}