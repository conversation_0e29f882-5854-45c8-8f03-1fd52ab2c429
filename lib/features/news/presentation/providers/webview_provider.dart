import 'package:flutter/foundation.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../../../../core/constants/app_constants.dart';

/// Provider for managing WebView state and interactions
class WebViewProvider extends ChangeNotifier {
  WebViewController? _controller;
  bool _isLoading = true;
  bool _canGoBack = false;
  bool _canGoForward = false;
  String _currentUrl = '';
  String _currentTitle = '';
  String? _errorMessage;
  int _loadingProgress = 0;

  // Getters
  WebViewController? get controller => _controller;
  bool get isLoading => _isLoading;
  bool get canGoBack => _canGoBack;
  bool get canGoForward => _canGoForward;
  String get currentUrl => _currentUrl;
  String get currentTitle => _currentTitle;
  String? get errorMessage => _errorMessage;
  int get loadingProgress => _loadingProgress;

  /// Initialize the WebView controller
  void initializeController(String initialUrl) {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setUserAgent(AppConstants.userAgent)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            _loadingProgress = progress;
            notifyListeners();
          },
          onPageStarted: (String url) {
            _setLoading(true);
            _currentUrl = url;
            _clearError();
            notifyListeners();
          },
          onPageFinished: (String url) async {
            _setLoading(false);
            _currentUrl = url;
            await _updateNavigationState();
            await _updateTitle();
            notifyListeners();
          },
          onWebResourceError: (WebResourceError error) {
            _setError('Failed to load page: ${error.description}');
            _setLoading(false);
          },
          onNavigationRequest: (NavigationRequest request) {
            // Allow all navigation requests
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(initialUrl));

    _currentUrl = initialUrl;
    notifyListeners();
  }

  /// Load a new URL
  Future<void> loadUrl(String url) async {
    if (_controller == null) return;
    
    _setLoading(true);
    _clearError();
    
    try {
      await _controller!.loadRequest(Uri.parse(url));
    } catch (e) {
      _setError('Failed to load URL: ${e.toString()}');
      _setLoading(false);
    }
  }

  /// Reload the current page
  Future<void> reload() async {
    if (_controller == null) return;
    
    _setLoading(true);
    _clearError();
    
    try {
      await _controller!.reload();
    } catch (e) {
      _setError('Failed to reload page: ${e.toString()}');
      _setLoading(false);
    }
  }

  /// Go back in history
  Future<void> goBack() async {
    if (_controller == null || !_canGoBack) return;
    
    try {
      await _controller!.goBack();
      await _updateNavigationState();
    } catch (e) {
      _setError('Failed to go back: ${e.toString()}');
    }
  }

  /// Go forward in history
  Future<void> goForward() async {
    if (_controller == null || !_canGoForward) return;
    
    try {
      await _controller!.goForward();
      await _updateNavigationState();
    } catch (e) {
      _setError('Failed to go forward: ${e.toString()}');
    }
  }

  /// Clear the WebView cache
  Future<void> clearCache() async {
    if (_controller == null) return;
    
    try {
      await _controller!.clearCache();
      await _controller!.clearLocalStorage();
    } catch (e) {
      _setError('Failed to clear cache: ${e.toString()}');
    }
  }

  /// Get the current page title
  Future<String?> getTitle() async {
    if (_controller == null) return null;
    
    try {
      return await _controller!.getTitle();
    } catch (e) {
      return null;
    }
  }

  /// Execute JavaScript in the WebView
  Future<String?> evaluateJavascript(String javascript) async {
    if (_controller == null) return null;
    
    try {
      final result = await _controller!.runJavaScriptReturningResult(javascript);
      return result.toString();
    } catch (e) {
      _setError('Failed to execute JavaScript: ${e.toString()}');
      return null;
    }
  }

  /// Dispose the controller
  void disposeController() {
    _controller = null;
    notifyListeners();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (!loading) {
      _loadingProgress = 100;
    }
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  Future<void> _updateNavigationState() async {
    if (_controller == null) return;
    
    try {
      _canGoBack = await _controller!.canGoBack();
      _canGoForward = await _controller!.canGoForward();
      notifyListeners();
    } catch (e) {
      // Ignore navigation state errors
    }
  }

  Future<void> _updateTitle() async {
    if (_controller == null) return;
    
    try {
      final title = await _controller!.getTitle();
      _currentTitle = title ?? '';
      notifyListeners();
    } catch (e) {
      // Ignore title errors
    }
  }
}
