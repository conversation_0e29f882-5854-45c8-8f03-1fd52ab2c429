import 'package:flutter/foundation.dart';
import '../../domain/entities/news_source.dart';
import '../../domain/entities/bookmark.dart';
import '../../domain/usecases/get_news_sources.dart';
import '../../domain/usecases/manage_bookmarks.dart';
import '../../domain/repositories/news_repository.dart';

/// Provider for managing news-related state
class NewsProvider extends ChangeNotifier {
  final GetNewsSources getNewsSources;
  final GetBookmarks getBookmarks;
  final AddBookmark addBookmark;
  final RemoveBookmark removeBookmark;
  final IsBookmarked isBookmarked;
  final NewsRepository repository;

  NewsProvider({
    required this.getNewsSources,
    required this.getBookmarks,
    required this.addBookmark,
    required this.removeBookmark,
    required this.isBookmarked,
    required this.repository,
  });

  // State variables
  List<NewsSource> _newsSources = [];
  NewsSource? _selectedNewsSource;
  List<Bookmark> _bookmarks = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<NewsSource> get newsSources => _newsSources;
  NewsSource? get selectedNewsSource => _selectedNewsSource;
  List<Bookmark> get bookmarks => _bookmarks;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  /// Initialize the provider by loading news sources and selected source
  Future<void> initialize() async {
    _setLoading(true);
    
    try {
      await loadNewsSources();
      await loadSelectedNewsSource();
      await loadBookmarks();
      _clearError();
    } catch (e) {
      _setError('Failed to initialize: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Load all available news sources
  Future<void> loadNewsSources() async {
    final result = await getNewsSources();
    result.fold(
      (failure) => _setError(failure.message),
      (sources) {
        _newsSources = sources;
        notifyListeners();
      },
    );
  }

  /// Load the currently selected news source
  Future<void> loadSelectedNewsSource() async {
    final result = await repository.getSelectedNewsSource();
    result.fold(
      (failure) => _setError(failure.message),
      (source) {
        _selectedNewsSource = source;
        notifyListeners();
      },
    );
  }

  /// Set the selected news source
  Future<void> setSelectedNewsSource(NewsSource source) async {
    _setLoading(true);
    
    final result = await repository.setSelectedNewsSource(source);
    result.fold(
      (failure) => _setError(failure.message),
      (_) {
        _selectedNewsSource = source;
        _clearError();
        notifyListeners();
      },
    );
    
    _setLoading(false);
  }

  /// Load all bookmarks
  Future<void> loadBookmarks() async {
    final result = await getBookmarks();
    result.fold(
      (failure) => _setError(failure.message),
      (bookmarks) {
        _bookmarks = bookmarks;
        notifyListeners();
      },
    );
  }

  /// Add a new bookmark
  Future<bool> addNewBookmark(Bookmark bookmark) async {
    final result = await addBookmark(bookmark);
    return result.fold(
      (failure) {
        _setError(failure.message);
        return false;
      },
      (_) {
        _bookmarks.add(bookmark);
        _clearError();
        notifyListeners();
        return true;
      },
    );
  }

  /// Remove a bookmark
  Future<bool> removeExistingBookmark(String bookmarkId) async {
    final result = await removeBookmark(bookmarkId);
    return result.fold(
      (failure) {
        _setError(failure.message);
        return false;
      },
      (_) {
        _bookmarks.removeWhere((bookmark) => bookmark.id == bookmarkId);
        _clearError();
        notifyListeners();
        return true;
      },
    );
  }

  /// Check if a URL is bookmarked
  Future<bool> checkIsBookmarked(String url) async {
    final result = await isBookmarked(url);
    return result.fold(
      (failure) => false,
      (isBookmarked) => isBookmarked,
    );
  }

  /// Get bookmark by URL
  Bookmark? getBookmarkByUrl(String url) {
    try {
      return _bookmarks.firstWhere((bookmark) => bookmark.url == url);
    } catch (e) {
      return null;
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
