import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';

/// Banner widget to show connectivity status
class ConnectivityBanner extends StatelessWidget {
  final String? message;
  final Color? backgroundColor;
  final Color? textColor;
  final IconData? icon;

  const ConnectivityBanner({
    super.key,
    this.message,
    this.backgroundColor,
    this.textColor,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.defaultPadding,
        vertical: AppConstants.smallPadding,
      ),
      color: backgroundColor ?? Colors.red[700],
      child: Row(
        children: [
          Icon(
            icon ?? Icons.wifi_off,
            color: textColor ?? Colors.white,
            size: 20,
          ),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
            child: Text(
              message ?? AppConstants.networkErrorMessage,
              style: TextStyle(
                color: textColor ?? Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Animated connectivity banner that slides in/out
class AnimatedConnectivityBanner extends StatefulWidget {
  final bool isVisible;
  final String? message;
  final Color? backgroundColor;
  final Color? textColor;
  final IconData? icon;

  const AnimatedConnectivityBanner({
    super.key,
    required this.isVisible,
    this.message,
    this.backgroundColor,
    this.textColor,
    this.icon,
  });

  @override
  State<AnimatedConnectivityBanner> createState() => _AnimatedConnectivityBannerState();
}

class _AnimatedConnectivityBannerState extends State<AnimatedConnectivityBanner>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: AppConstants.shortAnimationDuration,
      vsync: this,
    );
    _slideAnimation = Tween<double>(
      begin: -1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    if (widget.isVisible) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(AnimatedConnectivityBanner oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _slideAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value * 50),
          child: Opacity(
            opacity: _controller.value,
            child: ConnectivityBanner(
              message: widget.message,
              backgroundColor: widget.backgroundColor,
              textColor: widget.textColor,
              icon: widget.icon,
            ),
          ),
        );
      },
    );
  }
}

/// Success banner for when connectivity is restored
class ConnectivityRestoredBanner extends StatelessWidget {
  const ConnectivityRestoredBanner({super.key});

  @override
  Widget build(BuildContext context) {
    return ConnectivityBanner(
      message: 'Connection restored',
      backgroundColor: Colors.green[700],
      icon: Icons.wifi,
    );
  }
}
