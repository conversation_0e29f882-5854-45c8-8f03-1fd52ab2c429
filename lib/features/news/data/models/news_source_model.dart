import '../../domain/entities/news_source.dart';

/// Data model for NewsSource entity
class NewsSourceModel extends NewsSource {
  const NewsSourceModel({
    required super.id,
    required super.name,
    required super.url,
    required super.description,
    super.iconUrl,
    super.isActive,
  });

  /// Create NewsSourceModel from JSON
  factory NewsSourceModel.fromJson(Map<String, dynamic> json) {
    return NewsSourceModel(
      id: json['id'] as String,
      name: json['name'] as String,
      url: json['url'] as String,
      description: json['description'] as String,
      iconUrl: json['iconUrl'] as String? ?? '',
      isActive: json['isActive'] as bool? ?? true,
    );
  }

  /// Convert NewsSourceModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'url': url,
      'description': description,
      'iconUrl': iconUrl,
      'isActive': isActive,
    };
  }

  /// Create NewsSourceModel from NewsSource entity
  factory NewsSourceModel.fromEntity(NewsSource source) {
    return NewsSourceModel(
      id: source.id,
      name: source.name,
      url: source.url,
      description: source.description,
      iconUrl: source.iconUrl,
      isActive: source.isActive,
    );
  }

  /// Convert to NewsSource entity
  NewsSource toEntity() {
    return NewsSource(
      id: id,
      name: name,
      url: url,
      description: description,
      iconUrl: iconUrl,
      isActive: isActive,
    );
  }

  @override
  NewsSourceModel copyWith({
    String? id,
    String? name,
    String? url,
    String? description,
    String? iconUrl,
    bool? isActive,
  }) {
    return NewsSourceModel(
      id: id ?? this.id,
      name: name ?? this.name,
      url: url ?? this.url,
      description: description ?? this.description,
      iconUrl: iconUrl ?? this.iconUrl,
      isActive: isActive ?? this.isActive,
    );
  }
}
