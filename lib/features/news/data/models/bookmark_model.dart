import '../../domain/entities/bookmark.dart';

/// Data model for Bookmark entity
class BookmarkModel extends Bookmark {
  const BookmarkModel({
    required super.id,
    required super.title,
    required super.url,
    required super.description,
    required super.createdAt,
    super.imageUrl,
    super.sourceName,
  });

  /// Create BookmarkModel from JSON
  factory BookmarkModel.fromJson(Map<String, dynamic> json) {
    return BookmarkModel(
      id: json['id'] as String,
      title: json['title'] as String,
      url: json['url'] as String,
      description: json['description'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      imageUrl: json['imageUrl'] as String?,
      sourceName: json['sourceName'] as String?,
    );
  }

  /// Convert BookmarkModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'url': url,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'imageUrl': imageUrl,
      'sourceName': sourceName,
    };
  }

  /// Create BookmarkModel from Bookmark entity
  factory BookmarkModel.fromEntity(Bookmark bookmark) {
    return BookmarkModel(
      id: bookmark.id,
      title: bookmark.title,
      url: bookmark.url,
      description: bookmark.description,
      createdAt: bookmark.createdAt,
      imageUrl: bookmark.imageUrl,
      sourceName: bookmark.sourceName,
    );
  }

  /// Convert to Bookmark entity
  Bookmark toEntity() {
    return Bookmark(
      id: id,
      title: title,
      url: url,
      description: description,
      createdAt: createdAt,
      imageUrl: imageUrl,
      sourceName: sourceName,
    );
  }

  @override
  BookmarkModel copyWith({
    String? id,
    String? title,
    String? url,
    String? description,
    DateTime? createdAt,
    String? imageUrl,
    String? sourceName,
  }) {
    return BookmarkModel(
      id: id ?? this.id,
      title: title ?? this.title,
      url: url ?? this.url,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      imageUrl: imageUrl ?? this.imageUrl,
      sourceName: sourceName ?? this.sourceName,
    );
  }
}
