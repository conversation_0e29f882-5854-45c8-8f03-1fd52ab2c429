import '../../../../core/constants/app_constants.dart';
import '../../../../core/errors/failures.dart';
import '../models/news_source_model.dart';
import '../models/bookmark_model.dart';

/// Abstract class for local data operations
abstract class LocalDataSource {
  Future<List<NewsSourceModel>> getNewsSources();
  Future<NewsSourceModel?> getSelectedNewsSource();
  Future<void> setSelectedNewsSource(NewsSourceModel source);
  Future<List<BookmarkModel>> getBookmarks();
  Future<void> addBookmark(BookmarkModel bookmark);
  Future<void> removeBookmark(String bookmarkId);
  Future<bool> isBookmarked(String url);
  Future<void> clearBookmarks();
  Future<bool> isFirstLaunch();
  Future<void> setFirstLaunchComplete();
}

/// Simple implementation of LocalDataSource using in-memory storage
class LocalDataSourceImpl implements LocalDataSource {
  // In-memory storage for simplicity
  NewsSourceModel? _selectedSource;
  final List<BookmarkModel> _bookmarks = [];
  bool _isFirstLaunch = true;

  LocalDataSourceImpl();

  @override
  Future<List<NewsSourceModel>> getNewsSources() async {
    // Return single Tech News IO source
    return [
      const NewsSourceModel(
        id: 'tech_news_io',
        name: AppConstants.techNewsName,
        url: AppConstants.techNewsUrl,
        description: AppConstants.techNewsDescription,
        iconUrl: 'https://www.tech-news.io/favicon.ico',
        isActive: true,
      ),
    ];
  }

  @override
  Future<NewsSourceModel?> getSelectedNewsSource() async {
    if (_selectedSource != null) {
      return _selectedSource;
    }

    // Return default source if none selected
    final sources = await getNewsSources();
    _selectedSource = sources.isNotEmpty ? sources.first : null;
    return _selectedSource;
  }

  @override
  Future<void> setSelectedNewsSource(NewsSourceModel source) async {
    _selectedSource = source;
  }

  @override
  Future<List<BookmarkModel>> getBookmarks() async {
    return List.from(_bookmarks);
  }

  @override
  Future<void> addBookmark(BookmarkModel bookmark) async {
    // Check if bookmark already exists
    if (_bookmarks.any((b) => b.url == bookmark.url)) {
      throw const CacheFailure('Bookmark already exists');
    }

    _bookmarks.add(bookmark);
  }

  @override
  Future<void> removeBookmark(String bookmarkId) async {
    _bookmarks.removeWhere((bookmark) => bookmark.id == bookmarkId);
  }

  @override
  Future<bool> isBookmarked(String url) async {
    return _bookmarks.any((bookmark) => bookmark.url == url);
  }

  @override
  Future<void> clearBookmarks() async {
    _bookmarks.clear();
  }

  @override
  Future<bool> isFirstLaunch() async {
    return _isFirstLaunch;
  }

  @override
  Future<void> setFirstLaunchComplete() async {
    _isFirstLaunch = false;
  }


}
