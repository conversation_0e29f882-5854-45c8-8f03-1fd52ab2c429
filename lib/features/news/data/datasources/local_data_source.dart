import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/errors/failures.dart';
import '../models/news_source_model.dart';
import '../models/bookmark_model.dart';

/// Abstract class for local data operations
abstract class LocalDataSource {
  Future<List<NewsSourceModel>> getNewsSources();
  Future<NewsSourceModel?> getSelectedNewsSource();
  Future<void> setSelectedNewsSource(NewsSourceModel source);
  Future<List<BookmarkModel>> getBookmarks();
  Future<void> addBookmark(BookmarkModel bookmark);
  Future<void> removeBookmark(String bookmarkId);
  Future<bool> isBookmarked(String url);
  Future<void> clearBookmarks();
  Future<bool> isFirstLaunch();
  Future<void> setFirstLaunchComplete();
}

/// Implementation of LocalDataSource using SharedPreferences
class LocalDataSourceImpl implements LocalDataSource {
  final SharedPreferences sharedPreferences;

  LocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<List<NewsSourceModel>> getNewsSources() async {
    // Return predefined news sources
    return AppConstants.techNewsSources.asMap().entries.map((entry) {
      final index = entry.key;
      final url = entry.value;
      final name = _getSourceNameFromUrl(url);
      
      return NewsSourceModel(
        id: 'source_$index',
        name: name,
        url: url,
        description: 'Tech news from $name',
        iconUrl: _getFaviconUrl(url),
        isActive: true,
      );
    }).toList();
  }

  @override
  Future<NewsSourceModel?> getSelectedNewsSource() async {
    final sourceJson = sharedPreferences.getString(AppConstants.keySelectedNewsSource);
    if (sourceJson != null) {
      final sourceMap = json.decode(sourceJson) as Map<String, dynamic>;
      return NewsSourceModel.fromJson(sourceMap);
    }
    
    // Return default source if none selected
    final sources = await getNewsSources();
    return sources.isNotEmpty ? sources.first : null;
  }

  @override
  Future<void> setSelectedNewsSource(NewsSourceModel source) async {
    final sourceJson = json.encode(source.toJson());
    await sharedPreferences.setString(AppConstants.keySelectedNewsSource, sourceJson);
  }

  @override
  Future<List<BookmarkModel>> getBookmarks() async {
    final bookmarksJson = sharedPreferences.getStringList(AppConstants.keyBookmarks) ?? [];
    return bookmarksJson.map((bookmarkJson) {
      final bookmarkMap = json.decode(bookmarkJson) as Map<String, dynamic>;
      return BookmarkModel.fromJson(bookmarkMap);
    }).toList();
  }

  @override
  Future<void> addBookmark(BookmarkModel bookmark) async {
    final bookmarks = await getBookmarks();
    
    // Check if bookmark already exists
    if (bookmarks.any((b) => b.url == bookmark.url)) {
      throw const CacheFailure('Bookmark already exists');
    }
    
    bookmarks.add(bookmark);
    final bookmarksJson = bookmarks.map((b) => json.encode(b.toJson())).toList();
    await sharedPreferences.setStringList(AppConstants.keyBookmarks, bookmarksJson);
  }

  @override
  Future<void> removeBookmark(String bookmarkId) async {
    final bookmarks = await getBookmarks();
    bookmarks.removeWhere((bookmark) => bookmark.id == bookmarkId);
    final bookmarksJson = bookmarks.map((b) => json.encode(b.toJson())).toList();
    await sharedPreferences.setStringList(AppConstants.keyBookmarks, bookmarksJson);
  }

  @override
  Future<bool> isBookmarked(String url) async {
    final bookmarks = await getBookmarks();
    return bookmarks.any((bookmark) => bookmark.url == url);
  }

  @override
  Future<void> clearBookmarks() async {
    await sharedPreferences.remove(AppConstants.keyBookmarks);
  }

  @override
  Future<bool> isFirstLaunch() async {
    return !sharedPreferences.containsKey(AppConstants.keyFirstLaunch);
  }

  @override
  Future<void> setFirstLaunchComplete() async {
    await sharedPreferences.setBool(AppConstants.keyFirstLaunch, false);
  }

  // Helper methods
  String _getSourceNameFromUrl(String url) {
    final uri = Uri.parse(url);
    final domain = uri.host.toLowerCase();
    
    if (domain.contains('techcrunch')) return 'TechCrunch';
    if (domain.contains('theverge')) return 'The Verge';
    if (domain.contains('arstechnica')) return 'Ars Technica';
    if (domain.contains('wired')) return 'Wired';
    if (domain.contains('engadget')) return 'Engadget';
    if (domain.contains('cnet')) return 'CNET';
    if (domain.contains('zdnet')) return 'ZDNet';
    if (domain.contains('venturebeat')) return 'VentureBeat';
    if (domain.contains('mashable')) return 'Mashable';
    if (domain.contains('gizmodo')) return 'Gizmodo';
    
    return domain.replaceAll('www.', '').split('.').first.toUpperCase();
  }

  String _getFaviconUrl(String url) {
    final uri = Uri.parse(url);
    return '${uri.scheme}://${uri.host}/favicon.ico';
  }
}
