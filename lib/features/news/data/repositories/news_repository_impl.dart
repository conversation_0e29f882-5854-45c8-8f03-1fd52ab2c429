import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/utils/network_info.dart';
import '../../domain/entities/news_source.dart';
import '../../domain/entities/bookmark.dart';
import '../../domain/repositories/news_repository.dart';
import '../datasources/local_data_source.dart';
import '../models/news_source_model.dart';
import '../models/bookmark_model.dart';

/// Implementation of NewsRepository
class NewsRepositoryImpl implements NewsRepository {
  final LocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  NewsRepositoryImpl({
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<NewsSource>>> getNewsSources() async {
    try {
      final sources = await localDataSource.getNewsSources();
      return Right(sources.map((model) => model.toEntity()).toList());
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, NewsSource?>> getSelectedNewsSource() async {
    try {
      final source = await localDataSource.getSelectedNewsSource();
      return Right(source?.toEntity());
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> setSelectedNewsSource(NewsSource source) async {
    try {
      final model = NewsSourceModel.fromEntity(source);
      await localDataSource.setSelectedNewsSource(model);
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Bookmark>>> getBookmarks() async {
    try {
      final bookmarks = await localDataSource.getBookmarks();
      return Right(bookmarks.map((model) => model.toEntity()).toList());
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> addBookmark(Bookmark bookmark) async {
    try {
      final model = BookmarkModel.fromEntity(bookmark);
      await localDataSource.addBookmark(model);
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> removeBookmark(String bookmarkId) async {
    try {
      await localDataSource.removeBookmark(bookmarkId);
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> isBookmarked(String url) async {
    try {
      final isBookmarked = await localDataSource.isBookmarked(url);
      return Right(isBookmarked);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> clearBookmarks() async {
    try {
      await localDataSource.clearBookmarks();
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }
}
